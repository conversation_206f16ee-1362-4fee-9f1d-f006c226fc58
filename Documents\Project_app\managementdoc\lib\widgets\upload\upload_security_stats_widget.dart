import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/consolidated_upload_provider.dart';

class UploadSecurityStatsWidget extends StatelessWidget {
  final bool showDetailedStats;
  final VoidCallback? onTap;

  const UploadSecurityStatsWidget({
    super.key,
    this.showDetailedStats = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<UploadProvider>(
      builder: (context, uploadProvider, child) {
        final totalFiles = uploadProvider.totalFiles;
        final completedFiles = uploadProvider.completedFiles;
        final failedFiles = uploadProvider.failedFiles;
        final securityRejected = uploadProvider.securityRejectedFiles;
        final validationFailed = uploadProvider.validationFailedFiles;

        if (totalFiles == 0) {
          return const SizedBox.shrink();
        }

        return GestureDetector(
          onTap: onTap,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: securityRejected > 0
                    ? AppColors.securityWarning.withValues(alpha: 0.3)
                    : AppColors.border,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.shadow,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Icon(
                      Icons.security,
                      color: securityRejected > 0
                          ? AppColors.securityWarning
                          : AppColors.securitySafe,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Upload Security Status',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const Spacer(),
                    if (onTap != null)
                      Icon(
                        Icons.chevron_right,
                        color: AppColors.textSecondary,
                        size: 20,
                      ),
                  ],
                ),

                const SizedBox(height: 16),

                // Main stats row
                Row(
                  children: [
                    _buildStatItem(
                      'Total',
                      totalFiles.toString(),
                      AppColors.info,
                      Icons.folder_outlined,
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      'Safe',
                      completedFiles.toString(),
                      AppColors.securitySafe,
                      Icons.check_circle_outline,
                    ),
                    const SizedBox(width: 16),
                    _buildStatItem(
                      'Rejected',
                      failedFiles.toString(),
                      failedFiles > 0
                          ? AppColors.error
                          : AppColors.textSecondary,
                      Icons.block,
                    ),
                  ],
                ),

                if (showDetailedStats &&
                    (securityRejected > 0 || validationFailed > 0)) ...[
                  const SizedBox(height: 16),
                  const Divider(color: AppColors.border),
                  const SizedBox(height: 12),

                  // Detailed breakdown
                  Text(
                    'Rejection Breakdown',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),

                  if (securityRejected > 0)
                    _buildDetailItem(
                      'Security Threats',
                      securityRejected.toString(),
                      AppColors.securityDanger,
                      Icons.dangerous,
                      'Files blocked due to security concerns',
                    ),

                  if (validationFailed > 0)
                    _buildDetailItem(
                      'Validation Errors',
                      validationFailed.toString(),
                      AppColors.warning,
                      Icons.warning,
                      'Files rejected due to format or size issues',
                    ),
                ],

                if (securityRejected > 0) ...[
                  const SizedBox(height: 12),
                  _buildSecurityAlert(),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Expanded(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: color, size: 16),
              const SizedBox(width: 4),
              Text(
                value,
                style: GoogleFonts.poppins(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem(
    String title,
    String count,
    Color color,
    IconData icon,
    String description,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.poppins(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        count,
                        style: GoogleFonts.poppins(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: color,
                        ),
                      ),
                    ),
                  ],
                ),
                Text(
                  description,
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityAlert() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.securityWarning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.securityWarning.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.shield_outlined,
            color: AppColors.securityWarning,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Some files were blocked for security reasons. This helps protect your system from potential threats.',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Compact version for smaller spaces
class CompactUploadSecurityWidget extends StatelessWidget {
  final VoidCallback? onTap;

  const CompactUploadSecurityWidget({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Consumer<UploadProvider>(
      builder: (context, uploadProvider, child) {
        final securityRejected = uploadProvider.securityRejectedFiles;
        final totalFiles = uploadProvider.totalFiles;

        if (totalFiles == 0) return const SizedBox.shrink();

        final isSecure = securityRejected == 0;

        return GestureDetector(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: isSecure
                  ? AppColors.securitySafe.withValues(alpha: 0.1)
                  : AppColors.securityWarning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSecure
                    ? AppColors.securitySafe.withValues(alpha: 0.3)
                    : AppColors.securityWarning.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  isSecure ? Icons.verified_user : Icons.security,
                  color: isSecure
                      ? AppColors.securitySafe
                      : AppColors.securityWarning,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  isSecure ? 'Secure' : '$securityRejected blocked',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isSecure
                        ? AppColors.securitySafe
                        : AppColors.securityWarning,
                  ),
                ),
                if (onTap != null) ...[
                  const SizedBox(width: 4),
                  Icon(
                    Icons.chevron_right,
                    color: isSecure
                        ? AppColors.securitySafe
                        : AppColors.securityWarning,
                    size: 14,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}
