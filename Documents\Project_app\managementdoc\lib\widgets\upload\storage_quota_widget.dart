import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../core/constants/app_colors.dart';
import '../../providers/consolidated_upload_provider.dart';

/// Widget to display upload statistics and information
class StorageQuotaWidget extends StatelessWidget {
  final bool showDetails;
  final bool showCleanupButton;

  const StorageQuotaWidget({
    super.key,
    this.showDetails = true,
    this.showCleanupButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ConsolidatedUploadProvider>(
      builder: (context, uploadProvider, child) {
        // Simple storage info display
        final totalFiles = uploadProvider.totalFiles;
        final completedFiles = uploadProvider.completedFiles;
        final failedFiles = uploadProvider.failedFiles;

        if (totalFiles == 0) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isExceeded
                  ? AppColors.error
                  : isNearLimit
                  ? AppColors.warning
                  : AppColors.lightGray,
              width: isExceeded || isNearLimit ? 2 : 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Icon(
                    isExceeded
                        ? Icons.error
                        : isNearLimit
                        ? Icons.warning
                        : Icons.storage,
                    color: isExceeded
                        ? AppColors.error
                        : isNearLimit
                        ? AppColors.warning
                        : AppColors.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Storage Usage',
                    style: GoogleFonts.poppins(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const Spacer(),
                  if (showCleanupButton)
                    _buildCleanupButton(context, uploadProvider),
                  IconButton(
                    icon: const Icon(Icons.refresh, size: 18),
                    onPressed: () => uploadProvider.refreshStorageStats(),
                    tooltip: 'Refresh storage stats',
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Progress bar
              _buildProgressBar(usagePercentage, isNearLimit, isExceeded),

              const SizedBox(height: 8),

              // Usage text
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    usageString,
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  Text(
                    '${usagePercentage.toStringAsFixed(1)}%',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isExceeded
                          ? AppColors.error
                          : isNearLimit
                          ? AppColors.warning
                          : AppColors.textSecondary,
                    ),
                  ),
                ],
              ),

              // Warning message
              if (warningMessage != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isExceeded
                        ? AppColors.error.withValues(alpha: 0.1)
                        : AppColors.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isExceeded ? AppColors.error : AppColors.warning,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        isExceeded ? Icons.block : Icons.warning_amber,
                        color: isExceeded ? AppColors.error : AppColors.warning,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          warningMessage,
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: isExceeded
                                ? AppColors.error
                                : AppColors.warning,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Details
              if (showDetails) ...[
                const SizedBox(height: 12),
                _buildStorageDetails(storageStats),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildProgressBar(
    double percentage,
    bool isNearLimit,
    bool isExceeded,
  ) {
    return Container(
      height: 8,
      decoration: BoxDecoration(
        color: AppColors.lightGray,
        borderRadius: BorderRadius.circular(4),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: (percentage / 100).clamp(0.0, 1.0),
        child: Container(
          decoration: BoxDecoration(
            color: isExceeded
                ? AppColors.error
                : isNearLimit
                ? AppColors.warning
                : AppColors.primary,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  Widget _buildStorageDetails(Map<String, dynamic> storageStats) {
    final used = storageStats['used'] as int? ?? 0;
    final limit = storageStats['limit'] as int? ?? 0;
    final remaining = storageStats['remainingBytes'] as int? ?? 0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.lightBlue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildDetailRow('Used', _formatBytes(used)),
          const SizedBox(height: 4),
          _buildDetailRow('Total', _formatBytes(limit)),
          if (remaining >= 0) ...[
            const SizedBox(height: 4),
            _buildDetailRow('Available', _formatBytes(remaining)),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: AppColors.textSecondary,
          ),
        ),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppColors.textPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildCleanupButton(
    BuildContext context,
    EnhancedUploadProvider uploadProvider,
  ) {
    return TextButton.icon(
      onPressed: () => _showCleanupDialog(context, uploadProvider),
      icon: const Icon(Icons.cleaning_services, size: 16),
      label: const Text('Cleanup'),
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      ),
    );
  }

  void _showCleanupDialog(
    BuildContext context,
    EnhancedUploadProvider uploadProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Cleanup Orphaned Files',
          style: GoogleFonts.poppins(fontWeight: FontWeight.w600),
        ),
        content: Text(
          'This will remove files from storage that no longer have corresponding database records. This action cannot be undone.',
          style: GoogleFonts.poppins(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              try {
                final result = await uploadProvider.cleanupOrphanedFiles();

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Cleanup completed: ${result['deletedCount']} files removed',
                      ),
                      backgroundColor: AppColors.success,
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Cleanup failed: $e'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Cleanup'),
          ),
        ],
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
